using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    // ── ZIP EXTRACTOR ──
    
    public class ZipExtractor : IArchiveExtractor
    {
        public bool CanExtract(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".zip";
        }

        public System.Threading.Tasks.Task ExtractAsync(string archivePath, string extractPath)
        {
            try
            {
                // Secure path validation
                string canonicalExtractPath = Path.GetFullPath(extractPath);

                using var archive = ZipFile.OpenRead(archivePath);
                foreach (var entry in archive.Entries)
                {
                    // Prevent path traversal attacks
                    string destinationPath = Path.GetFullPath(Path.Combine(canonicalExtractPath, entry.FullName));
                    if (!destinationPath.StartsWith(canonicalExtractPath, StringComparison.OrdinalIgnoreCase))
                    {
                        throw new SecurityException($"Path traversal detected: {entry.FullName}");
                    }

                    // Skip directory entries
                    if (string.IsNullOrEmpty(entry.Name))
                    {
                        Directory.CreateDirectory(destinationPath);
                        continue;
                    }

                    // Ensure parent directory exists
                    Directory.CreateDirectory(Path.GetDirectoryName(destinationPath)!);

                    // Extract file
                    entry.ExtractToFile(destinationPath, true);
                }
                
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                return Task.FromException(new InvalidOperationException($"Failed to extract ZIP archive: {ex.Message}", ex));
            }
        }

        public Task<List<string>> ListContentsAsync(string archivePath)
        {
            try
            {
                var contents = new List<string>();

                using var archive = ZipFile.OpenRead(archivePath);
                contents.AddRange(archive.Entries.Select(entry => entry.FullName));
                
                return Task.FromResult(contents);
            }
            catch (Exception ex)
            {
                return Task.FromException<List<string>>(
                    new InvalidOperationException($"Failed to read ZIP archive contents: {ex.Message}", ex));
            }
        }

        public bool ValidateArchive(string archivePath)
        {
            try
            {
                using var archive = ZipFile.OpenRead(archivePath);
                // Try to read the first entry to validate the archive
                archive.Entries.FirstOrDefault();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }

    // ── 7-ZIP EXTRACTOR ──
    
    public class SevenZipExtractor : IArchiveExtractor
    {
        public bool CanExtract(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".7z" || extension == ".tar" || extension == ".gz" || extension == ".xz";
        }

        public Task ExtractAsync(string archivePath, string extractPath)
        {
            return Task.Run(() =>
            {
                try
                {
                    // Secure path validation
                    string canonicalExtractPath = Path.GetFullPath(extractPath);
                    string sevenZipPath = Find7ZipExecutable();
                    
                    if (string.IsNullOrEmpty(sevenZipPath))
                    {
                        throw new FileNotFoundException("7-Zip executable not found. Please install 7-Zip.");
                    }

                    // Secure process execution
                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = sevenZipPath,
                        Arguments = $"x \"{archivePath}\" -o\"{canonicalExtractPath}\" -y",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    };

                    using var process = System.Diagnostics.Process.Start(startInfo);
                    if (process == null)
                        throw new InvalidOperationException("Failed to start 7-Zip process");

                    process.WaitForExit();

                    if (process.ExitCode != 0)
                    {
                        var error = process.StandardError.ReadToEnd();
                        throw new InvalidOperationException($"7-Zip extraction failed: {error}");
                    }

                    // Validate extracted files don't escape destination
                    ValidateExtractedPaths(canonicalExtractPath);
                }
                catch (Exception ex) when (!(ex is FileNotFoundException))
                {
                    throw new InvalidOperationException($"Failed to extract 7z archive: {ex.Message}", ex);
                }
            });
        }

        public Task<List<string>> ListContentsAsync(string archivePath)
        {
            return Task.Run(() =>
            {
                var contents = new List<string>();
                
                try
                {
                    string sevenZipPath = Find7ZipExecutable();
                    
                    if (string.IsNullOrEmpty(sevenZipPath))
                    {
                        throw new FileNotFoundException("7-Zip executable not found.");
                    }

                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = sevenZipPath,
                        Arguments = $"l \"{archivePath}\"",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true
                    };

                    using var process = System.Diagnostics.Process.Start(startInfo);
                    if (process == null)
                        throw new InvalidOperationException("Failed to start 7-Zip process");

                    var output = process.StandardOutput.ReadToEnd();
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        // Parse 7z list output to extract file names
                        var lines = output.Split('\n');
                        bool inFilesList = false;
                        
                        foreach (var line in lines)
                        {
                            if (line.Contains("Name"))
                            {
                                inFilesList = true;
                                continue;
                            }
                            
                            if (inFilesList && !string.IsNullOrWhiteSpace(line) && !line.Contains("--"))
                            {
                                var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                                if (parts.Length > 0)
                                {
                                    contents.Add(parts[parts.Length - 1]);
                                }
                            }
                        }
                    }
                    else
                    {
                        var error = process.StandardError.ReadToEnd();
                        throw new InvalidOperationException($"7-Zip listing failed: {error}");
                    }
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Failed to list 7z archive contents: {ex.Message}", ex);
                }

                return contents;
            });
        }

        public bool ValidateArchive(string archivePath)
        {
            try
            {
                string sevenZipPath = Find7ZipExecutable();
                if (string.IsNullOrEmpty(sevenZipPath))
                    return false;

                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = sevenZipPath,
                    Arguments = $"t \"{archivePath}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process == null)
                    return false;

                process.WaitForExit();
                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        private void ValidateExtractedPaths(string basePath)
        {
            var extractedFiles = Directory.GetFiles(basePath, "*", SearchOption.AllDirectories);
            foreach (var file in extractedFiles)
            {
                string canonicalPath = Path.GetFullPath(file);
                if (!canonicalPath.StartsWith(basePath, StringComparison.OrdinalIgnoreCase))
                {
                    throw new SecurityException($"Path traversal detected in extracted file: {file}");
                }
            }
        }

        private string Find7ZipExecutable()
        {
            var possiblePaths = new[]
            {
                @"C:\Program Files\7-Zip\7z.exe",
                @"C:\Program Files (x86)\7-Zip\7z.exe"
            };

            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                    return path;
            }

            // Try to find in PATH
            try
            {
                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = "where",
                    Arguments = "7z.exe",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process != null)
                {
                    var output = process.StandardOutput.ReadToEnd().Trim();
                    process.WaitForExit();
                    
                    if (process.ExitCode == 0 && File.Exists(output))
                        return output;
                }
            }
            catch
            {
                // Ignore errors when trying to find in PATH
            }

            return string.Empty;
        }
    }

    // ── RAR EXTRACTOR ──
    
    public class RarExtractor : IArchiveExtractor
    {
        public bool CanExtract(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLowerInvariant();
            return extension == ".rar";
        }

        public Task ExtractAsync(string archivePath, string extractPath)
        {
            return Task.Run(() =>
            {
                try
                {
                    // Secure path validation
                    string canonicalExtractPath = Path.GetFullPath(extractPath);
                    string winRarPath = FindWinRarExecutable();
                    
                    if (string.IsNullOrEmpty(winRarPath))
                    {
                        throw new FileNotFoundException("WinRAR executable not found. Please install WinRAR or use a different archive format.");
                    }

                    // Secure process execution
                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = winRarPath,
                        Arguments = $"x -y \"{archivePath}\" \"{canonicalExtractPath}\\\"",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    };

                    using var process = System.Diagnostics.Process.Start(startInfo);
                    if (process == null)
                        throw new InvalidOperationException("Failed to start WinRAR process");

                    process.WaitForExit();

                    if (process.ExitCode != 0)
                    {
                        var error = process.StandardError.ReadToEnd();
                        throw new InvalidOperationException($"WinRAR extraction failed: {error}");
                    }

                    // Validate extracted files don't escape destination
                    ValidateExtractedPaths(canonicalExtractPath);
                }
                catch (Exception ex) when (!(ex is FileNotFoundException))
                {
                    throw new InvalidOperationException($"Failed to extract RAR archive: {ex.Message}", ex);
                }
            });
        }

        public Task<List<string>> ListContentsAsync(string archivePath)
        {
            return Task.Run(() =>
            {
                var contents = new List<string>();
                
                try
                {
                    string winRarPath = FindWinRarExecutable();
                    
                    if (string.IsNullOrEmpty(winRarPath))
                    {
                        throw new FileNotFoundException("WinRAR executable not found.");
                    }

                    var startInfo = new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = winRarPath,
                        Arguments = $"lb \"{archivePath}\"",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true
                    };

                    using var process = System.Diagnostics.Process.Start(startInfo);
                    if (process == null)
                        throw new InvalidOperationException("Failed to start WinRAR process");

                    var output = process.StandardOutput.ReadToEnd();
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        // Parse WinRAR list output to extract file names
                        var lines = output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                        contents.AddRange(lines);
                    }
                    else
                    {
                        var error = process.StandardError.ReadToEnd();
                        throw new InvalidOperationException($"WinRAR listing failed: {error}");
                    }
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"Failed to list RAR archive contents: {ex.Message}", ex);
                }

                return contents;
            });
        }

        public bool ValidateArchive(string archivePath)
        {
            try
            {
                string winRarPath = FindWinRarExecutable();
                if (string.IsNullOrEmpty(winRarPath))
                    return false;

                var startInfo = new System.Diagnostics.ProcessStartInfo
                {
                    FileName = winRarPath,
                    Arguments = $"t \"{archivePath}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true
                };

                using var process = System.Diagnostics.Process.Start(startInfo);
                if (process == null)
                    return false;

                process.WaitForExit();
                return process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        private void ValidateExtractedPaths(string basePath)
        {
            var extractedFiles = Directory.GetFiles(basePath, "*", SearchOption.AllDirectories);
            foreach (var file in extractedFiles)
            {
                string canonicalPath = Path.GetFullPath(file);
                if (!canonicalPath.StartsWith(basePath, StringComparison.OrdinalIgnoreCase))
                {
                    throw new SecurityException($"Path traversal detected in extracted file: {file}");
                }
            }
        }

        private string FindWinRarExecutable()
        {
            var possiblePaths = new[]
            {
                @"C:\Program Files\WinRAR\WinRAR.exe",
                @"C:\Program Files (x86)\WinRAR\WinRAR.exe",
                @"C:\Program Files\WinRAR\Rar.exe",
                @"C:\Program Files (x86)\WinRAR\Rar.exe"
            };

            foreach (var path in possiblePaths)
            {
                if (File.Exists(path))
                    return path;
            }

            return string.Empty;
        }
    }

    // ── ARCHIVE FORMAT DETECTOR ──
    
    public static class ArchiveFormatDetector
    {
        private static readonly Dictionary<string, byte[]> FileSignatures = new()
        {
            // ZIP file signatures
            [".zip"] = new byte[] { 0x50, 0x4B, 0x03, 0x04 }, // PK..
            // RAR file signatures
            [".rar"] = new byte[] { 0x52, 0x61, 0x72, 0x21, 0x1A, 0x07, 0x00 }, // Rar!...
            // 7z file signatures
            [".7z"] = new byte[] { 0x37, 0x7A, 0xBC, 0xAF, 0x27, 0x1C }, // 7z..'.
            // TAR file (often has various signatures)
            [".tar"] = new byte[] { 0x75, 0x73, 0x74, 0x61, 0x72 }, // ustar (at offset 257)
            // GZIP
            [".gz"] = new byte[] { 0x1F, 0x8B },
        };

        public static string DetectFormat(string filePath)
        {
            if (!File.Exists(filePath))
                return string.Empty;

            try
            {
                using var fs = File.OpenRead(filePath);
                var buffer = new byte[512]; // Read enough bytes to check various signatures
                var bytesRead = fs.Read(buffer, 0, buffer.Length);

                // Check file extension first (most reliable)
                var extension = Path.GetExtension(filePath).ToLowerInvariant();
                if (FileSignatures.ContainsKey(extension))
                {
                    var signature = FileSignatures[extension];
                    if (extension == ".tar")
                    {
                        // TAR signature is at offset 257
                        if (bytesRead > 262 && CheckSignature(buffer, signature, 257))
                            return extension;
                    }
                    else if (CheckSignature(buffer, signature, 0))
                    {
                        return extension;
                    }
                }

                // If extension doesn't match, try to detect by signature
                foreach (var kvp in FileSignatures)
                {
                    var signature = kvp.Value;
                    var format = kvp.Key;
                    
                    if (format == ".tar")
                    {
                        if (bytesRead > 262 && CheckSignature(buffer, signature, 257))
                            return format;
                    }
                    else if (CheckSignature(buffer, signature, 0))
                    {
                        return format;
                    }
                }

                return string.Empty;
            }
            catch
            {
                // If we can't read the file, fall back to extension
                return Path.GetExtension(filePath).ToLowerInvariant();
            }
        }

        private static bool CheckSignature(byte[] buffer, byte[] signature, int offset = 0)
        {
            if (buffer.Length < offset + signature.Length)
                return false;

            for (int i = 0; i < signature.Length; i++)
            {
                if (buffer[offset + i] != signature[i])
                    return false;
            }

            return true;
        }

        public static bool IsArchiveFile(string filePath)
        {
            var format = DetectFormat(filePath);
            return !string.IsNullOrEmpty(format);
        }

        public static IArchiveExtractor? GetExtractor(string filePath)
        {
            var format = DetectFormat(filePath);
            
            return format switch
            {
                ".zip" => new ZipExtractor(),
                ".7z" or ".tar" or ".gz" or ".xz" => new SevenZipExtractor(),
                ".rar" => new RarExtractor(),
                _ => null
            };
        }
    }

    // ── ARCHIVE VALIDATION UTILITIES ──
    
    public static class ArchiveValidationUtils
    {
        public static async Task<bool> ValidateArchiveIntegrity(string archivePath)
        {
            var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
            if (extractor == null)
                return false;

            try
            {
                // Use Task.Run to run the validation on a background thread since
                // ValidateArchive is a synchronous method that might be CPU-bound
                return await Task.Run(() => extractor.ValidateArchive(archivePath));
            }
            catch
            {
                return false;
            }
        }

        public static async Task<long> GetArchiveSize(string archivePath)
        {
            try
            {
                return await Task.Run(() => new FileInfo(archivePath).Length);
            }
            catch
            {
                return 0;
            }
        }

        public static async Task<int> CountArchiveEntries(string archivePath)
        {
            var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
            if (extractor == null)
                return 0;

            try
            {
                var contents = await extractor.ListContentsAsync(archivePath);
                return contents.Count;
            }
            catch
            {
                return 0;
            }
        }

        public static async Task<bool> ContainsPalFolder(string archivePath)
        {
            var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
            if (extractor == null)
                return false;

            try
            {
                var contents = await extractor.ListContentsAsync(archivePath);
                return contents.Any(entry => entry.Contains("Pal/") || entry.Contains("Pal\\"));
            }
            catch
            {
                return false;
            }
        }

        public static async Task<bool> ContainsUE4SSMods(string archivePath)
        {
            var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
            if (extractor == null)
                return false;

            try
            {
                var contents = await extractor.ListContentsAsync(archivePath);
                return contents.Any(entry => entry.EndsWith(".lua", StringComparison.OrdinalIgnoreCase) ||
                                           entry.EndsWith("enabled.txt", StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return false;
            }
        }

        public static async Task<bool> ContainsPakFiles(string archivePath)
        {
            var extractor = ArchiveFormatDetector.GetExtractor(archivePath);
            if (extractor == null)
                return false;

            try
            {
                var contents = await extractor.ListContentsAsync(archivePath);
                return contents.Any(entry => entry.EndsWith(".pak", StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return false;
            }
        }
    }
}