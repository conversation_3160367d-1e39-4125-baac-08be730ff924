using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Security;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    public class EnhancedInstallationEngine : IDisposable
    {
        private readonly string _palRoot;
        private readonly UE4SSDetector _detector;
        private readonly CacheManager _cache;
        private readonly EnhancedLogger _logger;
        private readonly List<IArchiveExtractor> _extractors;
        private bool _disposed = false;

        public EnhancedInstallationEngine(string palRoot, UE4SSDetector detector, CacheManager cache, EnhancedLogger logger)
        {
            _palRoot = ValidatePath(palRoot) ?? throw new ArgumentException("Invalid Palworld root path", nameof(palRoot));
            _detector = detector ?? throw new ArgumentNullException(nameof(detector));
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // Initialize archive extractors
            _extractors = new List<IArchiveExtractor>
            {
                new ZipExtractor(),
                new SevenZipExtractor(),
                new RarExtractor()
            };
        }

        public async Task<InstallationResult> InstallModAsync(string archivePath, InstallationOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            
            if (string.IsNullOrWhiteSpace(archivePath))
                throw new ArgumentException("Archive path cannot be null or empty", nameof(archivePath));
            
            if (!File.Exists(archivePath))
                throw new FileNotFoundException($"Archive file not found: {archivePath}");

            var operation = new InstallationOperation
            {
                ArchivePath = archivePath,
                TargetPath = _palRoot,
                Options = options,
                Status = InstallationStatus.Analyzing
            };

            _logger.StartPerformanceTracking($"ModInstallation_{Path.GetFileName(archivePath)}");
            
            try
            {
                _logger.LogOperation("ModInstallation", $"Starting installation of {Path.GetFileName(archivePath)}");

                // Step 1: Detect archive format and extract
                operation.Status = InstallationStatus.Extracting;
                var tempDir = await ExtractArchiveAsync(archivePath, cancellationToken);
                operation.ExtractedPath = tempDir;

                // Step 2: Analyze mod structure
                operation.Status = InstallationStatus.Analyzing;
                var modStructure = await AnalyzeModStructureAsync(tempDir, cancellationToken);
                operation.DetectedStructure = modStructure;

                _logger.LogInfo($"Detected mod type: {modStructure.ModType}", "ModAnalysis", 
                    new { ModStructure = modStructure });

                // Step 3: Validate compatibility
                operation.Status = InstallationStatus.ValidatingCompatibility;
                var compatibility = await ValidateCompatibilityAsync(modStructure, cancellationToken);
                operation.CompatibilityResult = compatibility;

                if (!compatibility.IsCompatible && !options.ForceInstall)
                {
                    throw new InvalidOperationException($"Mod is not compatible: {compatibility.Reason}");
                }

                // Step 4: Check for conflicts
                operation.Status = InstallationStatus.CheckingConflicts;
                var conflicts = await DetectInstallationConflictsAsync(modStructure, cancellationToken);
                operation.Conflicts = conflicts;

                if (conflicts.Count > 0 && options.ConflictResolution == ConflictResolution.Ask)
                {
                    // Return to caller for conflict resolution
                    operation.Status = InstallationStatus.AwaitingConflictResolution;
                    return new InstallationResult { Operation = operation, RequiresConflictResolution = true };
                }

                // Step 5: Create backup if requested
                if (options.CreateBackupBeforeInstall)
                {
                    operation.Status = InstallationStatus.CreatingBackup;
                    operation.BackupPath = await CreatePreInstallBackupAsync(modStructure, cancellationToken);
                }

                // Step 6: Install the mod
                operation.Status = InstallationStatus.Installing;
                await PerformInstallationAsync(operation, modStructure, cancellationToken);

                // Step 7: Post-installation tasks
                operation.Status = InstallationStatus.PostProcessing;
                await PostInstallationTasksAsync(operation, modStructure, cancellationToken);

                operation.Status = InstallationStatus.Completed;
                operation.EndTime = DateTime.Now;

                _logger.LogOperation("ModInstallation", 
                    $"Installation completed successfully in {operation.Duration.TotalSeconds:F1} seconds");

                return new InstallationResult 
                { 
                    Operation = operation, 
                    Success = true, 
                    InstalledFiles = operation.InstalledFiles.ToList() 
                };
            }
            catch (OperationCanceledException)
            {
                operation.Status = InstallationStatus.Cancelled;
                operation.EndTime = DateTime.Now;
                _logger.LogInfo("Installation cancelled by user", "ModInstallation");
                
                return new InstallationResult 
                { 
                    Operation = operation, 
                    Success = false, 
                    ErrorMessage = "Installation was cancelled" 
                };
            }
            catch (Exception ex)
            {
                operation.Status = InstallationStatus.Failed;
                operation.ErrorMessage = ex.Message;
                operation.EndTime = DateTime.Now;

                _logger.LogError("Installation failed", "ModInstallation", ex, 
                    new { ArchivePath = archivePath, Duration = operation.Duration.TotalSeconds });

                return new InstallationResult 
                { 
                    Operation = operation, 
                    Success = false, 
                    ErrorMessage = ex.Message 
                };
            }
            finally
            {
                _logger.StopPerformanceTracking($"ModInstallation_{Path.GetFileName(archivePath)}");
                
                // Cleanup temp directory
                if (!string.IsNullOrEmpty(operation.ExtractedPath) && Directory.Exists(operation.ExtractedPath))
                {
                    try
                    {
                        await Task.Run(() => Directory.Delete(operation.ExtractedPath, true), cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to cleanup temp directory: {ex.Message}", "Cleanup");
                    }
                }

                // Invalidate cache
                _detector.InvalidateCache();
            }
        }

        public async Task<BatchInstallationResult> InstallMultipleModsAsync(List<string> archivePaths, InstallationOptions options, CancellationToken cancellationToken = default)
        {
            ThrowIfDisposed();
            
            if (archivePaths == null || archivePaths.Count == 0)
                throw new ArgumentException("Archive paths list cannot be null or empty", nameof(archivePaths));

            var batchResult = new BatchInstallationResult
            {
                TotalMods = archivePaths.Count,
                StartTime = DateTime.Now
            };

            _logger.StartPerformanceTracking("BatchInstallation");
            _logger.LogOperation("BatchInstallation", $"Starting batch installation of {archivePaths.Count} mods");

            try
            {
                foreach (var archivePath in archivePaths)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    try
                    {
                        var result = await InstallModAsync(archivePath, options, cancellationToken);
                        batchResult.Results.Add(result);
                        
                        if (result.Success)
                        {
                            batchResult.SuccessfulInstalls++;
                        }
                        else
                        {
                            batchResult.FailedInstalls++;
                        }

                        // Update progress
                        batchResult.ProcessedMods = batchResult.Results.Count;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Batch installation failed for {Path.GetFileName(archivePath)}", "BatchInstallation", ex);
                        batchResult.FailedInstalls++;
                        batchResult.Results.Add(new InstallationResult
                        {
                            Success = false,
                            ErrorMessage = ex.Message,
                            Operation = new InstallationOperation { ArchivePath = archivePath, Status = InstallationStatus.Failed }
                        });
                    }
                }
            }
            catch (OperationCanceledException)
            {
                _logger.LogInfo("Batch installation cancelled by user", "BatchInstallation");
            }

            batchResult.EndTime = DateTime.Now;
            batchResult.Success = batchResult.FailedInstalls == 0;

            _logger.StopPerformanceTracking("BatchInstallation");
            _logger.LogOperation("BatchInstallation", 
                $"Batch installation completed: {batchResult.SuccessfulInstalls}/{batchResult.TotalMods} successful");

            return batchResult;
        }

        private async Task<string> ExtractArchiveAsync(string archivePath, CancellationToken cancellationToken)
        {
            var extractor = _extractors.FirstOrDefault(e => e.CanExtract(archivePath));
            if (extractor == null)
            {
                throw new NotSupportedException($"Archive format not supported: {Path.GetExtension(archivePath)}");
            }

            var tempDir = Path.Combine(Path.GetTempPath(), "PalworldMod_" + Guid.NewGuid().ToString("N")[..8]);
            var canonicalTempDir = Path.GetFullPath(tempDir);
            Directory.CreateDirectory(canonicalTempDir);

            _logger.LogDebug($"Extracting {Path.GetFileName(archivePath)} using {extractor.GetType().Name}", "Extraction");

            try
            {
                await extractor.ExtractAsync(archivePath, canonicalTempDir);
                _logger.LogDebug($"Extraction completed to: {canonicalTempDir}", "Extraction");
                return canonicalTempDir;
            }
            catch
            {
                // Cleanup on failure
                if (Directory.Exists(canonicalTempDir))
                {
                    try
                    {
                        Directory.Delete(canonicalTempDir, true);
                    }
                    catch { }
                }
                throw;
            }
        }

        private async Task<ModStructure> AnalyzeModStructureAsync(string extractedPath, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var structure = new ModStructure { RootPath = extractedPath };

                // Look for Pal folder structure (traditional mod)
                var palFolders = Directory.GetDirectories(extractedPath, "Pal", SearchOption.AllDirectories);
                if (palFolders.Length > 0)
                {
                    structure.ModType = ModType.TraditionalMod;
                    structure.ContentPath = palFolders[0];
                    structure.Files = Directory.GetFiles(palFolders[0], "*", SearchOption.AllDirectories)
                                              .Select(f => Path.GetRelativePath(palFolders[0], f))
                                              .ToList();
                }

                cancellationToken.ThrowIfCancellationRequested();

                // Check for UE4SS mod structure
                var ue4ssModFolders = Directory.GetDirectories(extractedPath, "*", SearchOption.AllDirectories)
                    .Where(d => Directory.GetFiles(d, "*.lua", SearchOption.TopDirectoryOnly).Length > 0 ||
                                File.Exists(Path.Combine(d, "enabled.txt")))
                    .ToArray();

                if (ue4ssModFolders.Length > 0)
                {
                    if (structure.ModType == ModType.Unknown)
                        structure.ModType = ModType.UE4SSMod;
                    else
                        structure.ModType = ModType.HybridMod;

                    structure.UE4SSMods = ue4ssModFolders.Select(d => new UE4SSModInfo
                    {
                        Name = Path.GetFileName(d) ?? "",
                        Path = d,
                        HasLuaScripts = Directory.GetFiles(d, "*.lua", SearchOption.AllDirectories).Length > 0,
                        HasEnabledFile = File.Exists(Path.Combine(d, "enabled.txt")),
                        IsValid = true
                    }).ToList();
                }

                cancellationToken.ThrowIfCancellationRequested();

                // Check for PalSchema mod structure
                var palSchemaFolders = Directory.GetDirectories(extractedPath, "*", SearchOption.AllDirectories)
                    .Where(d => Directory.Exists(Path.Combine(d, "blueprints")) ||
                                Directory.Exists(Path.Combine(d, "items")) ||
                                Directory.Exists(Path.Combine(d, "raw")))
                    .ToArray();

                if (palSchemaFolders.Length > 0)
                {
                    if (structure.ModType == ModType.Unknown)
                        structure.ModType = ModType.PalSchemaMod;
                    else
                        structure.ModType = ModType.HybridMod;

                    structure.PalSchemaMods = palSchemaFolders.Select(d => new PalSchemaModInfo
                    {
                        Name = Path.GetFileName(d) ?? "",
                        Path = d,
                        HasBlueprints = Directory.Exists(Path.Combine(d, "blueprints")),
                        HasItems = Directory.Exists(Path.Combine(d, "items")),
                        HasRaw = Directory.Exists(Path.Combine(d, "raw")),
                        IsValid = true
                    }).ToList();
                }

                // Check for loose PAK files
                var pakFiles = Directory.GetFiles(extractedPath, "*.pak", SearchOption.AllDirectories);
                if (pakFiles.Length > 0)
                {
                    if (structure.ModType == ModType.Unknown)
                        structure.ModType = ModType.PakMod;
                    else
                        structure.ModType = ModType.HybridMod;

                    structure.PakFiles = pakFiles.Select(f => new PakFileInfo
                    {
                        Name = Path.GetFileName(f) ?? "",
                        Path = f,
                        Size = new FileInfo(f).Length,
                        IsVanilla = Path.GetFileName(f).Equals("Pal-Windows.pak", StringComparison.OrdinalIgnoreCase)
                    }).ToList();
                }

                // Extract metadata if available
                ExtractModMetadata(structure);

                _logger.LogDebug($"Analyzed mod structure: {structure.ModType}", "ModAnalysis", structure);
                
                return structure;
            }, cancellationToken);
        }

        private void ExtractModMetadata(ModStructure structure)
        {
            // Look for common metadata files
            var metadataFiles = new[] { "mod.json", "metadata.json", "info.json", "readme.txt", "readme.md" };
            
            foreach (var metadataFile in metadataFiles)
            {
                var found = Directory.GetFiles(structure.RootPath, metadataFile, SearchOption.AllDirectories);
                if (found.Length > 0)
                {
                    try
                    {
                        var content = File.ReadAllText(found[0]);
                        if (metadataFile.EndsWith(".json"))
                        {
                            var metadata = JsonSerializer.Deserialize<Dictionary<string, object>>(content);
                            structure.Metadata = metadata ?? new Dictionary<string, object>();
                        }
                        else
                        {
                            structure.ReadmeContent = content;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to parse metadata file {metadataFile}: {ex.Message}", "MetadataExtraction");
                    }
                }
            }
        }

        private async Task<CompatibilityResult> ValidateCompatibilityAsync(ModStructure structure, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var result = new CompatibilityResult { IsCompatible = true };
                var issues = new List<string>();

                // Check UE4SS compatibility
                if (structure.UE4SSMods.Count > 0)
                {
                    var ue4ssStatus = _detector.DetectUE4SS();
                    if (ue4ssStatus.Status == UE4SSInstallStatus.NotInstalled)
                    {
                        issues.Add("UE4SS is required but not installed");
                        result.IsCompatible = false;
                    }
                    else if (ue4ssStatus.Status == UE4SSInstallStatus.PartiallyInstalled)
                    {
                        issues.Add("UE4SS installation is incomplete");
                        result.RequiresUE4SSRepair = true;
                    }
                }

                // Check PalSchema compatibility
                if (structure.PalSchemaMods.Count > 0)
                {
                    var palSchemaStatus = _detector.DetectPalSchema();
                    if (!palSchemaStatus.IsInstalled)
                    {
                        issues.Add("PalSchema is required but not installed");
                        result.IsCompatible = false;
                    }
                }

                // Check for invalid file paths
                foreach (var file in structure.Files)
                {
                    if (file.Contains("..") || Path.IsPathRooted(file))
                    {
                        issues.Add($"Invalid file path detected: {file}");
                        result.IsCompatible = false;
                    }
                }

                // Check for conflicting core files
                var coreFiles = new[] { "dwmapi.dll", "UE4SS.dll", "UE4SS-settings.ini" };
                foreach (var coreFile in coreFiles)
                {
                    if (structure.Files.Any(f => Path.GetFileName(f).Equals(coreFile, StringComparison.OrdinalIgnoreCase)))
                    {
                        issues.Add($"Mod contains core UE4SS file: {coreFile}");
                        result.RequiresUE4SSRepair = true;
                    }
                }

                result.Issues = issues;
                result.Reason = string.Join("; ", issues);

                return result;
            }, cancellationToken);
        }

        private async Task<List<InstallationConflict>> DetectInstallationConflictsAsync(ModStructure structure, CancellationToken cancellationToken)
        {
            return await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                var conflicts = new List<InstallationConflict>();

                // Check for existing files that would be overwritten
                foreach (var file in structure.Files)
                {
                    var targetPath = Path.Combine(_palRoot, "Pal", file);
                    if (File.Exists(targetPath))
                    {
                        var existingFile = new FileInfo(targetPath);
                        var newFile = new FileInfo(Path.Combine(structure.ContentPath, file));

                        conflicts.Add(new InstallationConflict
                        {
                            FilePath = file,
                            Type = ConflictType.FileExists,
                            ExistingFileDate = existingFile.LastWriteTime,
                            ExistingFileSize = existingFile.Length,
                            NewFileDate = newFile.LastWriteTime,
                            NewFileSize = newFile.Length,
                            Resolution = ConflictResolution.Ask
                        });
                    }
                }

                // Check for UE4SS mod name conflicts
                var ue4ssStatus = _detector.DetectUE4SS();
                foreach (var newMod in structure.UE4SSMods)
                {
                    if (ue4ssStatus.UserMods.Contains(newMod.Name, StringComparer.OrdinalIgnoreCase))
                    {
                        conflicts.Add(new InstallationConflict
                        {
                            FilePath = $"UE4SS Mod: {newMod.Name}",
                            Type = ConflictType.ModNameConflict,
                            Resolution = ConflictResolution.Ask
                        });
                    }

                    if (ue4ssStatus.CoreMods.Contains(newMod.Name, StringComparer.OrdinalIgnoreCase))
                    {
                        conflicts.Add(new InstallationConflict
                        {
                            FilePath = $"Core UE4SS Mod: {newMod.Name}",
                            Type = ConflictType.CoreModConflict,
                            Resolution = ConflictResolution.Skip
                        });
                    }
                }

                // Check for PalSchema mod conflicts
                var palSchemaStatus = _detector.DetectPalSchema();
                foreach (var newMod in structure.PalSchemaMods)
                {
                    if (palSchemaStatus.Mods.Any(m => m.Name.Equals(newMod.Name, StringComparison.OrdinalIgnoreCase)))
                    {
                        conflicts.Add(new InstallationConflict
                        {
                            FilePath = $"PalSchema Mod: {newMod.Name}",
                            Type = ConflictType.ModNameConflict,
                            Resolution = ConflictResolution.Ask
                        });
                    }
                }

                return conflicts;
            }, cancellationToken);
        }

        private async Task<string> CreatePreInstallBackupAsync(ModStructure structure, CancellationToken cancellationToken)
        {
            var backupDir = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.UserProfile),
                @"Downloads\Palworld Mods Backup",
                "PreInstall_" + DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss"));

            Directory.CreateDirectory(backupDir);

            // Create a targeted backup based on what will be affected
            var backupSettings = new BackupSettings
            {
                IncludeUE4SSCore = false,
                IncludeUE4SSCoreMods = false,
                IncludeUserMods = structure.UE4SSMods.Count > 0,
                IncludeGameContentMods = structure.Files.Count > 0 || structure.PakFiles.Count > 0,
                IncludePalSchemaSystem = false,
                IncludePalSchemaMods = structure.PalSchemaMods.Count > 0
            };

            var engine = new SmartBackupEngine(_palRoot, _detector, backupSettings);
            var filesToBackup = engine.GetFilesToBackup();

            await Task.Run(() =>
            {
                foreach (var sourceFile in filesToBackup)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    var relativePath = Path.GetRelativePath(_palRoot, sourceFile);
                    var destinationFile = Path.Combine(backupDir, relativePath);
                    
                    Directory.CreateDirectory(Path.GetDirectoryName(destinationFile)!);
                    File.Copy(sourceFile, destinationFile, true);
                }

                // Save metadata
                var metadata = engine.CreateBackupMetadata("Pre-installation automatic backup");
                var metadataPath = Path.Combine(backupDir, "backup_metadata.json");
                var metadataJson = JsonSerializer.Serialize(metadata, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(metadataPath, metadataJson);
            }, cancellationToken);

            _logger.LogInfo($"Pre-installation backup created: {backupDir}", "Backup");
            return backupDir;
        }

        private async Task PerformInstallationAsync(InstallationOperation operation, ModStructure structure, CancellationToken cancellationToken)
        {
            var totalFiles = structure.Files.Count + structure.UE4SSMods.Count + structure.PalSchemaMods.Count + structure.PakFiles.Count;
            operation.TotalFiles = totalFiles;

            await Task.Run(() =>
            {
                // Install traditional mod files
                if (!string.IsNullOrEmpty(structure.ContentPath))
                {
                    var palTargetPath = Path.Combine(_palRoot, "Pal");
                    foreach (var file in structure.Files)
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        
                        var sourcePath = Path.Combine(structure.ContentPath, file);
                        var targetPath = Path.Combine(palTargetPath, file);
                        
                        // Validate target path for security
                        var canonicalTarget = Path.GetFullPath(targetPath);
                        var canonicalPalTarget = Path.GetFullPath(palTargetPath);
                        if (!canonicalTarget.StartsWith(canonicalPalTarget, StringComparison.OrdinalIgnoreCase))
                        {
                            throw new SecurityException($"Path traversal detected: {file}");
                        }
                        
                        Directory.CreateDirectory(Path.GetDirectoryName(targetPath)!);
                        File.Copy(sourcePath, targetPath, true);
                        
                        operation.InstalledFiles.Add(targetPath);
                        operation.ProcessedFiles++;
                        
                        _logger.LogDebug($"Installed file: {file}", "ModInstallation");
                    }
                }

                // Install UE4SS mods
                var ue4ssModsPath = Path.Combine(_palRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods");
                foreach (var mod in structure.UE4SSMods)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    var targetModPath = Path.Combine(ue4ssModsPath, mod.Name);
                    
                    if (Directory.Exists(targetModPath) && operation.Options.ConflictResolution == ConflictResolution.Skip)
                    {
                        _logger.LogInfo($"Skipped existing UE4SS mod: {mod.Name}", "ModInstallation");
                        continue;
                    }

                    // Copy mod folder
                    CopyDirectory(mod.Path, targetModPath, true, cancellationToken);
                    operation.InstalledFiles.Add(targetModPath);
                    operation.ProcessedFiles++;
                    
                    _logger.LogInfo($"Installed UE4SS mod: {mod.Name}", "ModInstallation");
                }

                // Install PalSchema mods
                var palSchemaModsPath = Path.Combine(_palRoot, "Pal", "Binaries", "Win64", "ue4ss", "Mods", "palschema", "mods");
                if (Directory.Exists(palSchemaModsPath))
                {
                    foreach (var mod in structure.PalSchemaMods)
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        
                        var targetModPath = Path.Combine(palSchemaModsPath, mod.Name);
                        
                        if (Directory.Exists(targetModPath) && operation.Options.ConflictResolution == ConflictResolution.Skip)
                        {
                            _logger.LogInfo($"Skipped existing PalSchema mod: {mod.Name}", "ModInstallation");
                            continue;
                        }

                        CopyDirectory(mod.Path, targetModPath, true, cancellationToken);
                        operation.InstalledFiles.Add(targetModPath);
                        operation.ProcessedFiles++;
                        
                        _logger.LogInfo($"Installed PalSchema mod: {mod.Name}", "ModInstallation");
                    }
                }

                // Install PAK files
                var paksPath = Path.Combine(_palRoot, "Pal", "Content", "Paks");
                foreach (var pak in structure.PakFiles.Where(p => !p.IsVanilla))
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    var targetPath = Path.Combine(paksPath, pak.Name);
                    
                    if (File.Exists(targetPath) && operation.Options.ConflictResolution == ConflictResolution.Skip)
                    {
                        _logger.LogInfo($"Skipped existing PAK file: {pak.Name}", "ModInstallation");
                        continue;
                    }

                    File.Copy(pak.Path, targetPath, true);
                    operation.InstalledFiles.Add(targetPath);
                    operation.ProcessedFiles++;
                    
                    _logger.LogInfo($"Installed PAK file: {pak.Name}", "ModInstallation");
                }
            }, cancellationToken);

            _logger.LogInfo($"Installation completed: {operation.ProcessedFiles}/{operation.TotalFiles} items installed", "ModInstallation");
        }

        private async Task PostInstallationTasksAsync(InstallationOperation operation, ModStructure structure, CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                // Update mod manifest
                UpdateModManifest(operation, structure);

                // Log installation record
                LogInstallationRecord(operation, structure);

                // Update cache
                _detector.InvalidateCache();
            }, cancellationToken);
        }

        private void UpdateModManifest(InstallationOperation operation, ModStructure structure)
        {
            try
            {
                var manifestPath = Path.Combine(_palRoot, "mod_manifest.json");
                var manifest = new Dictionary<string, object>();

                if (File.Exists(manifestPath))
                {
                    try
                    {
                        var existingJson = File.ReadAllText(manifestPath);
                        manifest = JsonSerializer.Deserialize<Dictionary<string, object>>(existingJson) ?? new();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to read existing manifest: {ex.Message}", "Manifest");
                    }
                }

                // Add new installation record
                var installRecord = new
                {
                    ArchiveName = Path.GetFileName(operation.ArchivePath),
                    InstallDate = DateTime.Now,
                    ModType = structure.ModType.ToString(),
                    InstalledFiles = operation.InstalledFiles.Select(f => Path.GetRelativePath(_palRoot, f)).ToList(),
                    UE4SSMods = structure.UE4SSMods.Select(m => m.Name).ToList(),
                    PalSchemaMods = structure.PalSchemaMods.Select(m => m.Name).ToList(),
                    PakFiles = structure.PakFiles.Where(p => !p.IsVanilla).Select(p => p.Name).ToList()
                };

                manifest[$"install_{DateTime.Now:yyyyMMdd_HHmmss}"] = installRecord;

                var manifestJson = JsonSerializer.Serialize(manifest, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(manifestPath, manifestJson);
                _logger.LogDebug("Mod manifest updated", "Manifest");
            }
            catch (Exception ex)
            {
                _logger.LogError("Failed to update mod manifest", "Manifest", ex);
            }
        }

        private void LogInstallationRecord(InstallationOperation operation, ModStructure structure)
        {
            var installationRecord = new
            {
                operation.ArchivePath,
                ArchiveName = Path.GetFileName(operation.ArchivePath),
                operation.StartTime,
                operation.EndTime,
                Duration = operation.Duration.TotalSeconds,
                structure.ModType,
                FileCount = operation.ProcessedFiles,
                UE4SSModCount = structure.UE4SSMods.Count,
                PalSchemaModCount = structure.PalSchemaMods.Count,
                PakFileCount = structure.PakFiles.Count,
                operation.BackupPath,
                ConflictCount = operation.Conflicts.Count
            };

            _logger.LogOperation("InstallationComplete", "Mod installation completed", installationRecord);
        }

        private void CopyDirectory(string sourceDir, string targetDir, bool recursive, CancellationToken cancellationToken)
        {
            var dir = new DirectoryInfo(sourceDir);
            DirectoryInfo[] dirs = dir.GetDirectories();

            Directory.CreateDirectory(targetDir);

            foreach (FileInfo file in dir.GetFiles())
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                string targetFilePath = Path.Combine(targetDir, file.Name);
                file.CopyTo(targetFilePath, true);
            }

            if (recursive)
            {
                foreach (DirectoryInfo subDir in dirs)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    string newTargetDir = Path.Combine(targetDir, subDir.Name);
                    CopyDirectory(subDir.FullName, newTargetDir, true, cancellationToken);
                }
            }
        }

        private static string? ValidatePath(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return null;
                
            try
            {
                // Secure path validation to prevent path traversal
                string canonicalPath = Path.GetFullPath(path);
                
                // Ensure the path exists and contains a Palworld game
                if (!Directory.Exists(canonicalPath))
                    return null;
                    
                var gameExePath = Path.Combine(canonicalPath, "Palworld.exe");
                if (!File.Exists(gameExePath))
                    return null;
                    
                return canonicalPath;
            }
            catch
            {
                return null;
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(EnhancedInstallationEngine));
        }

        public async Task<UninstallationResult> UninstallModAsync(string modIdentifier, UninstallationOptions options)
        {
            // Implementation for uninstalling specific mods
            // This would read from the manifest and remove specific installations
            throw new NotImplementedException("Mod uninstallation will be implemented in the next phase");
        }

        public async Task<RollbackResult> RollbackInstallationAsync(string installationId)
        {
            // Implementation for rolling back specific installations
            throw new NotImplementedException("Installation rollback will be implemented in the next phase");
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Clean up resources
                _disposed = true;
            }
        }

        ~EnhancedInstallationEngine()
        {
            Dispose(false);
        }
    }
}