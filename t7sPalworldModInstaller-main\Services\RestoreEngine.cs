using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using ModInstallerApp.Models;

namespace ModInstallerApp.Services
{
    public class RestoreEngine
    {
        private readonly string _palRoot;
        private readonly UE4SSDetector _detector;
        private readonly CacheManager _cache;
        private readonly Action<string> _logger;

        public RestoreEngine(string palRoot, UE4SSDetector detector, CacheManager cache, Action<string> logger)
        {
            _palRoot = palRoot;
            _detector = detector;
            _cache = cache;
            _logger = logger;
        }

        public async Task<RestoreOperation> RestoreBackupAsync(string backupPath, BackupConflictResolution resolution = BackupConflictResolution.BackupFirst)
        {
            var operation = new RestoreOperation
            {
                BackupPath = backupPath,
                TargetPath = _palRoot,
                Status = RestoreOperationStatus.Pending
            };

            try
            {
                _logger($"Starting restore from: {Path.GetFileName(backupPath)}");
                operation.Status = RestoreOperationStatus.AnalyzingConflicts;

                // Load backup metadata
                var metadata = await LoadBackupMetadata(backupPath);
                if (metadata == null)
                {
                    throw new InvalidOperationException("Invalid backup: metadata not found");
                }

                // Detect current state and conflicts
                var currentUE4SS = _detector.DetectUE4SS();
                var currentPalSchema = _detector.DetectPalSchema();
                var conflicts = DetectConflicts(metadata, currentUE4SS, currentPalSchema);

                if (conflicts.Count > 0)
                {
                    _logger($"Detected {conflicts.Count} potential conflicts");
                    
                    switch (resolution)
                    {
                        case BackupConflictResolution.Cancel:
                            operation.Status = RestoreOperationStatus.Cancelled;
                            return operation;

                        case BackupConflictResolution.BackupFirst:
                            operation.Status = RestoreOperationStatus.CreatingPreBackup;
                            operation.PreRestoreBackupPath = await CreatePreRestoreBackup();
                            _logger($"Pre-restore backup created: {operation.PreRestoreBackupPath}");
                            break;

                        case BackupConflictResolution.SmartMerge:
                            _logger("Using smart merge strategy");
                            break;

                        case BackupConflictResolution.ForceRestore:
                            _logger("Force restore - will overwrite current state");
                            break;
                    }
                }

                // Perform the actual restore
                operation.Status = RestoreOperationStatus.Restoring;
                await PerformRestore(operation, metadata, resolution);

                operation.Status = RestoreOperationStatus.Completed;
                operation.EndTime = DateTime.Now;
                _logger($"Restore completed successfully in {operation.Duration.TotalSeconds:F1} seconds");

                // Invalidate caches after restore
                _detector.InvalidateCache();

                return operation;
            }
            catch (Exception ex)
            {
                operation.Status = RestoreOperationStatus.Failed;
                operation.ErrorMessage = ex.Message;
                operation.EndTime = DateTime.Now;
                _logger($"Restore failed: {ex.Message}");
                throw;
            }
        }

        private async Task<BackupMetadata?> LoadBackupMetadata(string backupPath)
        {
            try
            {
                if (Directory.Exists(backupPath))
                {
                    // Directory backup
                    var metadataPath = Path.Combine(backupPath, "backup_metadata.json");
                    if (File.Exists(metadataPath))
                    {
                        var json = await File.ReadAllTextAsync(metadataPath);
                        return JsonSerializer.Deserialize<BackupMetadata>(json);
                    }
                }
                else if (File.Exists(backupPath) && Path.GetExtension(backupPath).Equals(".zip", StringComparison.OrdinalIgnoreCase))
                {
                    // ZIP backup
                    using var archive = ZipFile.OpenRead(backupPath);
                    var metadataEntry = archive.GetEntry("backup_metadata.json");
                    if (metadataEntry != null)
                    {
                        using var stream = metadataEntry.Open();
                        using var reader = new StreamReader(stream);
                        var json = await reader.ReadToEndAsync();
                        return JsonSerializer.Deserialize<BackupMetadata>(json);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger($"Failed to load backup metadata: {ex.Message}");
            }

            return null;
        }

        private List<BackupConflict> DetectConflicts(BackupMetadata metadata, UE4SSStatus currentUE4SS, PalSchemaStatus currentPalSchema)
        {
            var conflicts = new List<BackupConflict>();

            // UE4SS conflicts
            if (metadata.UE4SSStatus.Status != currentUE4SS.Status)
            {
                conflicts.Add(new BackupConflict
                {
                    FilePath = "UE4SS Installation",
                    Type = BackupConflictType.FileExists,
                    BackupFileDate = metadata.BackupDate,
                    CurrentFileDate = currentUE4SS.LastChecked
                });
            }

            // Core mods conflicts
            if (metadata.UE4SSStatus.CoreModsPresent != currentUE4SS.CoreModsPresent)
            {
                conflicts.Add(new BackupConflict
                {
                    FilePath = "UE4SS Core Mods",
                    Type = BackupConflictType.FileSizeDifferent,
                    BackupFileDate = metadata.BackupDate,
                    CurrentFileDate = currentUE4SS.LastChecked
                });
            }

            // User mods conflicts
            var addedMods = currentUE4SS.UserMods.Except(metadata.UE4SSStatus.UserMods).ToList();
            var removedMods = metadata.UE4SSStatus.UserMods.Except(currentUE4SS.UserMods).ToList();

            foreach (var mod in addedMods)
            {
                conflicts.Add(new BackupConflict
                {
                    FilePath = $"User Mod: {mod}",
                    Type = BackupConflictType.FileExists,
                    BackupFileDate = metadata.BackupDate,
                    CurrentFileDate = DateTime.Now
                });
            }

            // PalSchema conflicts
            if (metadata.PalSchemaStatus.IsInstalled != currentPalSchema.IsInstalled)
            {
                conflicts.Add(new BackupConflict
                {
                    FilePath = "PalSchema Installation",
                    Type = BackupConflictType.FileExists,
                    BackupFileDate = metadata.BackupDate,
                    CurrentFileDate = currentPalSchema.LastChecked
                });
            }

            if (metadata.PalSchemaStatus.Mods.Count != currentPalSchema.Mods.Count)
            {
                conflicts.Add(new BackupConflict
                {
                    FilePath = "PalSchema Mods",
                    Type = BackupConflictType.FileSizeDifferent,
                    BackupFileDate = metadata.BackupDate,
                    CurrentFileDate = currentPalSchema.LastChecked
                });
            }

            return conflicts;
        }

        private async Task<string> CreatePreRestoreBackup()
        {
            var backupDir = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.UserProfile),
                @"Downloads\Palworld Mods Backup",
                "PreRestore_" + DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss"));

            Directory.CreateDirectory(backupDir);

            // Create a quick backup of current state
            var currentUE4SS = _detector.DetectUE4SS();
            var currentPalSchema = _detector.DetectPalSchema();
            
            var backupSettings = new BackupSettings
            {
                IncludeUE4SSCore = currentUE4SS.Status != UE4SSInstallStatus.NotInstalled,
                IncludeUE4SSCoreMods = currentUE4SS.CoreModsPresent > 0,
                IncludeUserMods = currentUE4SS.UserMods.Count > 0,
                IncludeGameContentMods = true,
                IncludePalSchemaSystem = currentPalSchema.IsInstalled,
                IncludePalSchemaMods = currentPalSchema.Mods.Count > 0
            };

            var engine = new SmartBackupEngine(_palRoot, _detector, backupSettings);
            var filesToBackup = engine.GetFilesToBackup();

            foreach (var sourceFile in filesToBackup)
            {
                var relativePath = Path.GetRelativePath(_palRoot, sourceFile);
                var destinationFile = Path.Combine(backupDir, relativePath);
                
                Directory.CreateDirectory(Path.GetDirectoryName(destinationFile)!);
                File.Copy(sourceFile, destinationFile, true);
            }

            // Save metadata
            var metadata = engine.CreateBackupMetadata("Pre-restore automatic backup");
            var metadataPath = Path.Combine(backupDir, "backup_metadata.json");
            var metadataJson = JsonSerializer.Serialize(metadata, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(metadataPath, metadataJson);

            return backupDir;
        }

        private async Task PerformRestore(RestoreOperation operation, BackupMetadata metadata, BackupConflictResolution resolution)
        {
            List<string> filesToRestore;

            if (Directory.Exists(operation.BackupPath))
            {
                // Directory backup
                filesToRestore = Directory.GetFiles(operation.BackupPath, "*", SearchOption.AllDirectories)
                    .Where(f => !Path.GetFileName(f).Equals("backup_metadata.json"))
                    .ToList();
            }
            else
            {
                // ZIP backup - extract to temp directory first
                var tempDir = Path.Combine(Path.GetTempPath(), "PalworldRestore_" + Guid.NewGuid().ToString("N")[..8]);
                Directory.CreateDirectory(tempDir);

                try
                {
                    ZipFile.ExtractToDirectory(operation.BackupPath, tempDir);
                    filesToRestore = Directory.GetFiles(tempDir, "*", SearchOption.AllDirectories)
                        .Where(f => !Path.GetFileName(f).Equals("backup_metadata.json"))
                        .ToList();

                    // Update paths to point to temp directory
                    operation.BackupPath = tempDir;
                }
                catch (Exception ex)
                {
                    Directory.Delete(tempDir, true);
                    throw new InvalidOperationException($"Failed to extract backup: {ex.Message}");
                }
            }

            operation.TotalFiles = filesToRestore.Count;
            operation.TotalBytes = filesToRestore.Sum(f => new FileInfo(f).Length);

            foreach (var sourceFile in filesToRestore)
            {
                try
                {
                    var relativePath = Path.GetRelativePath(operation.BackupPath, sourceFile);
                    var destinationFile = Path.Combine(_palRoot, relativePath);

                    // Apply resolution strategy
                    if (File.Exists(destinationFile) && resolution == BackupConflictResolution.SmartMerge)
                    {
                        // Smart merge logic - skip UE4SS core files if they're newer
                        if (IsUE4SSCoreFile(relativePath) && File.GetLastWriteTime(destinationFile) > File.GetLastWriteTime(sourceFile))
                        {
                            _logger($"Skipping newer UE4SS core file: {relativePath}");
                            operation.FilesProcessed++;
                            continue;
                        }
                    }

                    Directory.CreateDirectory(Path.GetDirectoryName(destinationFile)!);
                    File.Copy(sourceFile, destinationFile, true);
                    
                    operation.RestoredFiles.Add(relativePath);
                    operation.FilesProcessed++;
                    operation.BytesProcessed += new FileInfo(sourceFile).Length;

                    _logger($"Restored: {relativePath}");
                }
                catch (Exception ex)
                {
                    var relativePath = Path.GetRelativePath(operation.BackupPath, sourceFile);
                    operation.FailedFiles.Add(relativePath);
                    _logger($"Failed to restore {relativePath}: {ex.Message}");
                }
            }

            _logger($"Restore completed: {operation.FilesProcessed}/{operation.TotalFiles} files restored");
        }

        private bool IsUE4SSCoreFile(string relativePath)
        {
            var coreFiles = new[]
            {
                @"Pal\Binaries\Win64\dwmapi.dll",
                @"Pal\Binaries\Win64\ue4ss\UE4SS.dll",
                @"Pal\Binaries\Win64\ue4ss\UE4SS-settings.ini",
                @"Pal\Binaries\Win64\ue4ss\LICENSE"
            };

            return coreFiles.Any(cf => relativePath.Equals(cf, StringComparison.OrdinalIgnoreCase));
        }

        public List<BackupInfo> GetAvailableBackups(string backupRoot)
        {
            var backups = new List<BackupInfo>();

            if (!Directory.Exists(backupRoot))
                return backups;

            // Directory backups
            foreach (var dir in Directory.GetDirectories(backupRoot))
            {
                var metadataPath = Path.Combine(dir, "backup_metadata.json");
                if (File.Exists(metadataPath))
                {
                    try
                    {
                        var json = File.ReadAllText(metadataPath);
                        var metadata = JsonSerializer.Deserialize<BackupMetadata>(json);
                        if (metadata != null)
                        {
                            backups.Add(new BackupInfo
                            {
                                Name = Path.GetFileName(dir),
                                Path = dir,
                                Created = metadata.BackupDate,
                                Size = Directory.GetFiles(dir, "*", SearchOption.AllDirectories).Sum(f => new FileInfo(f).Length),
                                Metadata = metadata,
                                IsValid = true
                            });
                        }
                    }
                    catch
                    {
                        // Invalid backup, skip
                    }
                }
            }

            // ZIP backups
            foreach (var zipFile in Directory.GetFiles(backupRoot, "*.zip"))
            {
                try
                {
                    using var archive = ZipFile.OpenRead(zipFile);
                    var metadataEntry = archive.GetEntry("backup_metadata.json");
                    if (metadataEntry != null)
                    {
                        using var stream = metadataEntry.Open();
                        using var reader = new StreamReader(stream);
                        var json = reader.ReadToEnd();
                        var metadata = JsonSerializer.Deserialize<BackupMetadata>(json);
                        if (metadata != null)
                        {
                            backups.Add(new BackupInfo
                            {
                                Name = Path.GetFileNameWithoutExtension(zipFile),
                                Path = zipFile,
                                Created = metadata.BackupDate,
                                Size = new FileInfo(zipFile).Length,
                                Metadata = metadata,
                                IsValid = true
                            });
                        }
                    }
                }
                catch
                {
                    // Invalid backup, skip
                }
            }

            return backups.OrderByDescending(b => b.Created).ToList();
        }
    }
}