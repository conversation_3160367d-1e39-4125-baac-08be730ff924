using System;
using System.Drawing;
using System.Windows.Forms;
using ModInstallerApp.Models;

namespace ModInstallerApp.UI
{
    // This class provides UI components for the installation process
    public static class InstallationUIHelpers
    {
        // Utility methods for installation UI

        /// <summary>
        /// Creates a progress panel for installation operations
        /// </summary>
        public static Panel CreateProgressPanel()
        {
            var panel = new Panel { Dock = DockStyle.Fill };
            var progressBar = new ProgressBar { Dock = DockStyle.Top, Height = 24 };
            var label = new Label { Dock = DockStyle.Top, Text = "Installation Progress", Height = 20 };

            panel.Controls.Add(progressBar);
            panel.Controls.Add(label);

            return panel;
        }

        /// <summary>
        /// Creates a status label for installation feedback
        /// </summary>
        public static Label CreateStatusLabel()
        {
            return new Label
            {
                Dock = DockStyle.Bottom,
                Height = 24,
                TextAlign = ContentAlignment.MiddleLeft,
                Text = "Ready to install"
            };
        }

        /// <summary>
        /// Formats a file size for display
        /// </summary>
        public static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len /= 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}